{"permalink": "/blog/mdx-blog-post", "editUrl": "https://github.com/facebook/docusaurus/tree/main/packages/create-docusaurus/templates/shared/blog/2021-08-01-mdx-blog-post.mdx", "source": "@site/blog/2021-08-01-mdx-blog-post.mdx", "title": "MDX Blog Post", "description": "Blog posts support Docusaurus Markdown features, such as MDX.", "date": "2021-08-01T00:00:00.000Z", "tags": [{"inline": false, "label": "Docusaurus", "permalink": "/blog/tags/docusaurus", "description": "Docusaurus tag description"}], "readingTime": 0.27, "hasTruncateMarker": true, "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Docusaurus maintainer", "url": "https://sebastienlorber.com", "page": {"permalink": "/blog/authors/all-sebastien-lorber-articles"}, "socials": {"x": "https://x.com/sebastienlorber", "linkedin": "https://www.linkedin.com/in/sebastienlorber/", "github": "https://github.com/slorber", "newsletter": "https://thisweekinreact.com"}, "imageURL": "https://github.com/slorber.png", "key": "slorber"}], "frontMatter": {"slug": "mdx-blog-post", "title": "MDX Blog Post", "authors": ["slorber"], "tags": ["<PERSON>cusaurus"]}, "unlisted": false, "prevItem": {"title": "Welcome", "permalink": "/blog/welcome"}, "nextItem": {"title": "Long Blog Post", "permalink": "/blog/long-blog-post"}}