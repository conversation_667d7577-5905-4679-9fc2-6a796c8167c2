{"allContent": {"docusaurus-plugin-css-cascade-layers": {}, "docusaurus-plugin-content-docs": {"default": {"loadedVersions": [{"versionName": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "path": "/docs", "tagsPath": "/docs/tags", "editUrl": "https://github.com/facebook/docusaurus/tree/main/packages/create-docusaurus/templates/shared/docs", "editUrlLocalized": "https://github.com/facebook/docusaurus/tree/main/packages/create-docusaurus/templates/shared/i18n/en/docusaurus-plugin-content-docs/current", "isLast": true, "routePriority": -1, "sidebarFilePath": "/Users/<USER>/MyData/Sam.Code/Web.Projects/by_AI/docusaurus-hello/sidebars.ts", "contentPath": "/Users/<USER>/MyData/Sam.Code/Web.Projects/by_AI/docusaurus-hello/docs", "contentPathLocalized": "/Users/<USER>/MyData/Sam.Code/Web.Projects/by_AI/docusaurus-hello/i18n/en/docusaurus-plugin-content-docs/current", "docs": [{"id": "advanced-examples", "title": "高级可执行示例", "description": "这里展示一些更复杂的React组件示例。", "source": "@site/docs/advanced-examples.md", "sourceDirName": ".", "slug": "/advanced-examples", "permalink": "/docs/advanced-examples", "draft": false, "unlisted": false, "editUrl": "https://github.com/facebook/docusaurus/tree/main/packages/create-docusaurus/templates/shared/docs/advanced-examples.md", "tags": [], "version": "current", "frontMatter": {}, "sidebar": "tutorialSidebar", "previous": {"title": "Translate your site", "permalink": "/docs/tutorial-extras/translate-your-site"}, "next": {"title": "可执行代码示例", "permalink": "/docs/executable-examples"}}, {"id": "executable-examples", "title": "可执行代码示例", "description": "这个页面展示了如何在Docusaurus中嵌入可执行的代码示例。", "source": "@site/docs/executable-examples.md", "sourceDirName": ".", "slug": "/executable-examples", "permalink": "/docs/executable-examples", "draft": false, "unlisted": false, "editUrl": "https://github.com/facebook/docusaurus/tree/main/packages/create-docusaurus/templates/shared/docs/executable-examples.md", "tags": [], "version": "current", "frontMatter": {}, "sidebar": "tutorialSidebar", "previous": {"title": "高级可执行示例", "permalink": "/docs/advanced-examples"}}, {"id": "intro", "title": "Docusaurus 可执行代码示例", "description": "欢迎来到 Docusaurus 可执行代码示例 项目！", "source": "@site/docs/intro.md", "sourceDirName": ".", "slug": "/intro", "permalink": "/docs/intro", "draft": false, "unlisted": false, "editUrl": "https://github.com/facebook/docusaurus/tree/main/packages/create-docusaurus/templates/shared/docs/intro.md", "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"sidebar_position": 1}, "sidebar": "tutorialSidebar", "next": {"title": "Tutorial - Basics", "permalink": "/docs/category/tutorial---basics"}}, {"id": "tutorial-basics/congratulations", "title": "Congratulations!", "description": "You have just learned the basics of Docusaurus and made some changes to the initial template.", "source": "@site/docs/tutorial-basics/congratulations.md", "sourceDirName": "tutorial-basics", "slug": "/tutorial-basics/congratulations", "permalink": "/docs/tutorial-basics/congratulations", "draft": false, "unlisted": false, "editUrl": "https://github.com/facebook/docusaurus/tree/main/packages/create-docusaurus/templates/shared/docs/tutorial-basics/congratulations.md", "tags": [], "version": "current", "sidebarPosition": 6, "frontMatter": {"sidebar_position": 6}, "sidebar": "tutorialSidebar", "previous": {"title": "Deploy your site", "permalink": "/docs/tutorial-basics/deploy-your-site"}, "next": {"title": "Tutorial - Extras", "permalink": "/docs/category/tutorial---extras"}}, {"id": "tutorial-basics/create-a-blog-post", "title": "Create a Blog Post", "description": "Docusaurus creates a page for each blog post, but also a blog index page, a tag system, an RSS feed...", "source": "@site/docs/tutorial-basics/create-a-blog-post.md", "sourceDirName": "tutorial-basics", "slug": "/tutorial-basics/create-a-blog-post", "permalink": "/docs/tutorial-basics/create-a-blog-post", "draft": false, "unlisted": false, "editUrl": "https://github.com/facebook/docusaurus/tree/main/packages/create-docusaurus/templates/shared/docs/tutorial-basics/create-a-blog-post.md", "tags": [], "version": "current", "sidebarPosition": 3, "frontMatter": {"sidebar_position": 3}, "sidebar": "tutorialSidebar", "previous": {"title": "Create a Document", "permalink": "/docs/tutorial-basics/create-a-document"}, "next": {"title": "Markdown Features", "permalink": "/docs/tutorial-basics/markdown-features"}}, {"id": "tutorial-basics/create-a-document", "title": "Create a Document", "description": "Documents are groups of pages connected through:", "source": "@site/docs/tutorial-basics/create-a-document.md", "sourceDirName": "tutorial-basics", "slug": "/tutorial-basics/create-a-document", "permalink": "/docs/tutorial-basics/create-a-document", "draft": false, "unlisted": false, "editUrl": "https://github.com/facebook/docusaurus/tree/main/packages/create-docusaurus/templates/shared/docs/tutorial-basics/create-a-document.md", "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"sidebar_position": 2}, "sidebar": "tutorialSidebar", "previous": {"title": "Create a Page", "permalink": "/docs/tutorial-basics/create-a-page"}, "next": {"title": "Create a Blog Post", "permalink": "/docs/tutorial-basics/create-a-blog-post"}}, {"id": "tutorial-basics/create-a-page", "title": "Create a Page", "description": "Add Markdown or React files to src/pages to create a standalone page:", "source": "@site/docs/tutorial-basics/create-a-page.md", "sourceDirName": "tutorial-basics", "slug": "/tutorial-basics/create-a-page", "permalink": "/docs/tutorial-basics/create-a-page", "draft": false, "unlisted": false, "editUrl": "https://github.com/facebook/docusaurus/tree/main/packages/create-docusaurus/templates/shared/docs/tutorial-basics/create-a-page.md", "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"sidebar_position": 1}, "sidebar": "tutorialSidebar", "previous": {"title": "Tutorial - Basics", "permalink": "/docs/category/tutorial---basics"}, "next": {"title": "Create a Document", "permalink": "/docs/tutorial-basics/create-a-document"}}, {"id": "tutorial-basics/deploy-your-site", "title": "Deploy your site", "description": "Docusaurus is a static-site-generator (also called Jamstack).", "source": "@site/docs/tutorial-basics/deploy-your-site.md", "sourceDirName": "tutorial-basics", "slug": "/tutorial-basics/deploy-your-site", "permalink": "/docs/tutorial-basics/deploy-your-site", "draft": false, "unlisted": false, "editUrl": "https://github.com/facebook/docusaurus/tree/main/packages/create-docusaurus/templates/shared/docs/tutorial-basics/deploy-your-site.md", "tags": [], "version": "current", "sidebarPosition": 5, "frontMatter": {"sidebar_position": 5}, "sidebar": "tutorialSidebar", "previous": {"title": "Markdown Features", "permalink": "/docs/tutorial-basics/markdown-features"}, "next": {"title": "Congratulations!", "permalink": "/docs/tutorial-basics/congratulations"}}, {"id": "tutorial-basics/markdown-features", "title": "Markdown Features", "description": "Docusaurus supports Markdown and a few additional features.", "source": "@site/docs/tutorial-basics/markdown-features.mdx", "sourceDirName": "tutorial-basics", "slug": "/tutorial-basics/markdown-features", "permalink": "/docs/tutorial-basics/markdown-features", "draft": false, "unlisted": false, "editUrl": "https://github.com/facebook/docusaurus/tree/main/packages/create-docusaurus/templates/shared/docs/tutorial-basics/markdown-features.mdx", "tags": [], "version": "current", "sidebarPosition": 4, "frontMatter": {"sidebar_position": 4}, "sidebar": "tutorialSidebar", "previous": {"title": "Create a Blog Post", "permalink": "/docs/tutorial-basics/create-a-blog-post"}, "next": {"title": "Deploy your site", "permalink": "/docs/tutorial-basics/deploy-your-site"}}, {"id": "tutorial-extras/manage-docs-versions", "title": "Manage Docs Versions", "description": "Docusaurus can manage multiple versions of your docs.", "source": "@site/docs/tutorial-extras/manage-docs-versions.md", "sourceDirName": "tutorial-extras", "slug": "/tutorial-extras/manage-docs-versions", "permalink": "/docs/tutorial-extras/manage-docs-versions", "draft": false, "unlisted": false, "editUrl": "https://github.com/facebook/docusaurus/tree/main/packages/create-docusaurus/templates/shared/docs/tutorial-extras/manage-docs-versions.md", "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"sidebar_position": 1}, "sidebar": "tutorialSidebar", "previous": {"title": "Tutorial - Extras", "permalink": "/docs/category/tutorial---extras"}, "next": {"title": "Translate your site", "permalink": "/docs/tutorial-extras/translate-your-site"}}, {"id": "tutorial-extras/translate-your-site", "title": "Translate your site", "description": "Let's translate docs/intro.md to French.", "source": "@site/docs/tutorial-extras/translate-your-site.md", "sourceDirName": "tutorial-extras", "slug": "/tutorial-extras/translate-your-site", "permalink": "/docs/tutorial-extras/translate-your-site", "draft": false, "unlisted": false, "editUrl": "https://github.com/facebook/docusaurus/tree/main/packages/create-docusaurus/templates/shared/docs/tutorial-extras/translate-your-site.md", "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"sidebar_position": 2}, "sidebar": "tutorialSidebar", "previous": {"title": "Manage Docs Versions", "permalink": "/docs/tutorial-extras/manage-docs-versions"}, "next": {"title": "高级可执行示例", "permalink": "/docs/advanced-examples"}}], "drafts": [], "sidebars": {"tutorialSidebar": [{"type": "doc", "id": "intro"}, {"type": "category", "label": "Tutorial - Basics", "collapsible": true, "collapsed": true, "items": [{"type": "doc", "id": "tutorial-basics/create-a-page"}, {"type": "doc", "id": "tutorial-basics/create-a-document"}, {"type": "doc", "id": "tutorial-basics/create-a-blog-post"}, {"type": "doc", "id": "tutorial-basics/markdown-features"}, {"type": "doc", "id": "tutorial-basics/deploy-your-site"}, {"type": "doc", "id": "tutorial-basics/congratulations"}], "link": {"type": "generated-index", "description": "5 minutes to learn the most important Docusaurus concepts.", "slug": "/category/tutorial---basics", "permalink": "/docs/category/tutorial---basics"}}, {"type": "category", "label": "Tutorial - Extras", "collapsible": true, "collapsed": true, "items": [{"type": "doc", "id": "tutorial-extras/manage-docs-versions"}, {"type": "doc", "id": "tutorial-extras/translate-your-site"}], "link": {"type": "generated-index", "slug": "/category/tutorial---extras", "permalink": "/docs/category/tutorial---extras"}}, {"type": "doc", "id": "advanced-examples"}, {"type": "doc", "id": "executable-examples"}]}}]}}, "docusaurus-plugin-content-blog": {"default": {"blogSidebarTitle": "Recent posts", "blogPosts": [{"id": "welcome", "metadata": {"permalink": "/blog/welcome", "editUrl": "https://github.com/facebook/docusaurus/tree/main/packages/create-docusaurus/templates/shared/blog/2021-08-26-welcome/index.md", "source": "@site/blog/2021-08-26-welcome/index.md", "title": "Welcome", "description": "Docusaurus blogging features are powered by the blog plugin.", "date": "2021-08-26T00:00:00.000Z", "tags": [{"inline": false, "label": "Facebook", "permalink": "/blog/tags/facebook", "description": "Facebook tag description"}, {"inline": false, "label": "Hello", "permalink": "/blog/tags/hello", "description": "Hello tag description"}, {"inline": false, "label": "Docusaurus", "permalink": "/blog/tags/docusaurus", "description": "Docusaurus tag description"}], "readingTime": 0.56, "hasTruncateMarker": true, "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Docusaurus maintainer", "url": "https://sebastienlorber.com", "page": {"permalink": "/blog/authors/all-sebastien-lorber-articles"}, "socials": {"x": "https://x.com/sebastienlorber", "linkedin": "https://www.linkedin.com/in/sebastienlorber/", "github": "https://github.com/slorber", "newsletter": "https://thisweekinreact.com"}, "imageURL": "https://github.com/slorber.png", "key": "slorber"}, {"name": "<PERSON><PERSON><PERSON>", "title": "Ex-Meta Staff Engineer, Co-founder GreatFrontEnd", "url": "https://linkedin.com/in/yangshun", "page": {"permalink": "/blog/authors/yangshun"}, "socials": {"x": "https://x.com/yangshunz", "linkedin": "https://www.linkedin.com/in/yangshun/", "github": "https://github.com/yangshun", "newsletter": "https://www.greatfrontend.com"}, "imageURL": "https://github.com/yangshun.png", "key": "yang<PERSON>n"}], "frontMatter": {"slug": "welcome", "title": "Welcome", "authors": ["slorber", "yang<PERSON>n"], "tags": ["facebook", "hello", "<PERSON>cusaurus"]}, "unlisted": false, "nextItem": {"title": "MDX Blog Post", "permalink": "/blog/mdx-blog-post"}}, "content": "[Docusaurus blogging features](https://docusaurus.io/docs/blog) are powered by the [blog plugin](https://docusaurus.io/docs/api/plugins/@docusaurus/plugin-content-blog).\n\nHere are a few tips you might find useful.\n\n<!-- truncate -->\n\nSimply add Markdown files (or folders) to the `blog` directory.\n\nRegular blog authors can be added to `authors.yml`.\n\nThe blog post date can be extracted from filenames, such as:\n\n- `2019-05-30-welcome.md`\n- `2019-05-30-welcome/index.md`\n\nA blog post folder can be convenient to co-locate blog post images:\n\n![Docusaurus Plushie](./docusaurus-plushie-banner.jpeg)\n\nThe blog supports tags as well!\n\n**And if you don't want a blog**: just delete this directory, and use `blog: false` in your Docusaurus config."}, {"id": "mdx-blog-post", "metadata": {"permalink": "/blog/mdx-blog-post", "editUrl": "https://github.com/facebook/docusaurus/tree/main/packages/create-docusaurus/templates/shared/blog/2021-08-01-mdx-blog-post.mdx", "source": "@site/blog/2021-08-01-mdx-blog-post.mdx", "title": "MDX Blog Post", "description": "Blog posts support Docusaurus Markdown features, such as MDX.", "date": "2021-08-01T00:00:00.000Z", "tags": [{"inline": false, "label": "Docusaurus", "permalink": "/blog/tags/docusaurus", "description": "Docusaurus tag description"}], "readingTime": 0.27, "hasTruncateMarker": true, "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Docusaurus maintainer", "url": "https://sebastienlorber.com", "page": {"permalink": "/blog/authors/all-sebastien-lorber-articles"}, "socials": {"x": "https://x.com/sebastienlorber", "linkedin": "https://www.linkedin.com/in/sebastienlorber/", "github": "https://github.com/slorber", "newsletter": "https://thisweekinreact.com"}, "imageURL": "https://github.com/slorber.png", "key": "slorber"}], "frontMatter": {"slug": "mdx-blog-post", "title": "MDX Blog Post", "authors": ["slorber"], "tags": ["<PERSON>cusaurus"]}, "unlisted": false, "prevItem": {"title": "Welcome", "permalink": "/blog/welcome"}, "nextItem": {"title": "Long Blog Post", "permalink": "/blog/long-blog-post"}}, "content": "Blog posts support [Docusaurus Markdown features](https://docusaurus.io/docs/markdown-features), such as [MDX](https://mdxjs.com/).\n\n:::tip\n\nUse the power of React to create interactive blog posts.\n\n:::\n\n{/* truncate */}\n\nFor example, use JSX to create an interactive button:\n\n```js\n<button onClick={() => alert('button clicked!')}>Click me!</button>\n```\n\n<button onClick={() => alert('button clicked!')}>Click me!</button>"}, {"id": "long-blog-post", "metadata": {"permalink": "/blog/long-blog-post", "editUrl": "https://github.com/facebook/docusaurus/tree/main/packages/create-docusaurus/templates/shared/blog/2019-05-29-long-blog-post.md", "source": "@site/blog/2019-05-29-long-blog-post.md", "title": "Long Blog Post", "description": "This is the summary of a very long blog post,", "date": "2019-05-29T00:00:00.000Z", "tags": [{"inline": false, "label": "Hello", "permalink": "/blog/tags/hello", "description": "Hello tag description"}, {"inline": false, "label": "Docusaurus", "permalink": "/blog/tags/docusaurus", "description": "Docusaurus tag description"}], "readingTime": 2.04, "hasTruncateMarker": true, "authors": [{"name": "<PERSON><PERSON><PERSON>", "title": "Ex-Meta Staff Engineer, Co-founder GreatFrontEnd", "url": "https://linkedin.com/in/yangshun", "page": {"permalink": "/blog/authors/yangshun"}, "socials": {"x": "https://x.com/yangshunz", "linkedin": "https://www.linkedin.com/in/yangshun/", "github": "https://github.com/yangshun", "newsletter": "https://www.greatfrontend.com"}, "imageURL": "https://github.com/yangshun.png", "key": "yang<PERSON>n"}], "frontMatter": {"slug": "long-blog-post", "title": "Long Blog Post", "authors": "yang<PERSON>n", "tags": ["hello", "<PERSON>cusaurus"]}, "unlisted": false, "prevItem": {"title": "MDX Blog Post", "permalink": "/blog/mdx-blog-post"}, "nextItem": {"title": "First Blog Post", "permalink": "/blog/first-blog-post"}}, "content": "This is the summary of a very long blog post,\n\nUse a `<!--` `truncate` `-->` comment to limit blog post size in the list view.\n\n<!-- truncate -->\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet"}, {"id": "first-blog-post", "metadata": {"permalink": "/blog/first-blog-post", "editUrl": "https://github.com/facebook/docusaurus/tree/main/packages/create-docusaurus/templates/shared/blog/2019-05-28-first-blog-post.md", "source": "@site/blog/2019-05-28-first-blog-post.md", "title": "First Blog Post", "description": "Lorem ipsum dolor sit amet...", "date": "2019-05-28T00:00:00.000Z", "tags": [{"inline": false, "label": "<PERSON><PERSON>", "permalink": "/blog/tags/hola", "description": "Hola tag description"}, {"inline": false, "label": "Docusaurus", "permalink": "/blog/tags/docusaurus", "description": "Docusaurus tag description"}], "readingTime": 0.13, "hasTruncateMarker": true, "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Docusaurus maintainer", "url": "https://sebastienlorber.com", "page": {"permalink": "/blog/authors/all-sebastien-lorber-articles"}, "socials": {"x": "https://x.com/sebastienlorber", "linkedin": "https://www.linkedin.com/in/sebastienlorber/", "github": "https://github.com/slorber", "newsletter": "https://thisweekinreact.com"}, "imageURL": "https://github.com/slorber.png", "key": "slorber"}, {"name": "<PERSON><PERSON><PERSON>", "title": "Ex-Meta Staff Engineer, Co-founder GreatFrontEnd", "url": "https://linkedin.com/in/yangshun", "page": {"permalink": "/blog/authors/yangshun"}, "socials": {"x": "https://x.com/yangshunz", "linkedin": "https://www.linkedin.com/in/yangshun/", "github": "https://github.com/yangshun", "newsletter": "https://www.greatfrontend.com"}, "imageURL": "https://github.com/yangshun.png", "key": "yang<PERSON>n"}], "frontMatter": {"slug": "first-blog-post", "title": "First Blog Post", "authors": ["slorber", "yang<PERSON>n"], "tags": ["hola", "<PERSON>cusaurus"]}, "unlisted": false, "prevItem": {"title": "Long Blog Post", "permalink": "/blog/long-blog-post"}}, "content": "Lorem ipsum dolor sit amet...\n\n<!-- truncate -->\n\n...consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet"}], "blogListPaginated": [{"items": ["welcome", "mdx-blog-post", "long-blog-post", "first-blog-post"], "metadata": {"permalink": "/blog", "page": 1, "postsPerPage": 10, "totalPages": 1, "totalCount": 4, "blogDescription": "Blog", "blogTitle": "Blog"}}], "blogTags": {"/blog/tags/facebook": {"inline": false, "label": "Facebook", "permalink": "/blog/tags/facebook", "description": "Facebook tag description", "items": ["welcome"], "pages": [{"items": ["welcome"], "metadata": {"permalink": "/blog/tags/facebook", "page": 1, "postsPerPage": 10, "totalPages": 1, "totalCount": 1, "blogDescription": "Blog", "blogTitle": "Blog"}}], "unlisted": false}, "/blog/tags/hello": {"inline": false, "label": "Hello", "permalink": "/blog/tags/hello", "description": "Hello tag description", "items": ["welcome", "long-blog-post"], "pages": [{"items": ["welcome", "long-blog-post"], "metadata": {"permalink": "/blog/tags/hello", "page": 1, "postsPerPage": 10, "totalPages": 1, "totalCount": 2, "blogDescription": "Blog", "blogTitle": "Blog"}}], "unlisted": false}, "/blog/tags/docusaurus": {"inline": false, "label": "Docusaurus", "permalink": "/blog/tags/docusaurus", "description": "Docusaurus tag description", "items": ["welcome", "mdx-blog-post", "long-blog-post", "first-blog-post"], "pages": [{"items": ["welcome", "mdx-blog-post", "long-blog-post", "first-blog-post"], "metadata": {"permalink": "/blog/tags/docusaurus", "page": 1, "postsPerPage": 10, "totalPages": 1, "totalCount": 4, "blogDescription": "Blog", "blogTitle": "Blog"}}], "unlisted": false}, "/blog/tags/hola": {"inline": false, "label": "<PERSON><PERSON>", "permalink": "/blog/tags/hola", "description": "Hola tag description", "items": ["first-blog-post"], "pages": [{"items": ["first-blog-post"], "metadata": {"permalink": "/blog/tags/hola", "page": 1, "postsPerPage": 10, "totalPages": 1, "totalCount": 1, "blogDescription": "Blog", "blogTitle": "Blog"}}], "unlisted": false}}, "blogTagsListPath": "/blog/tags", "authorsMap": {"yangshun": {"name": "<PERSON><PERSON><PERSON>", "title": "Ex-Meta Staff Engineer, Co-founder GreatFrontEnd", "url": "https://linkedin.com/in/yangshun", "page": {"permalink": "/blog/authors/yangshun"}, "socials": {"x": "https://x.com/yangshunz", "linkedin": "https://www.linkedin.com/in/yangshun/", "github": "https://github.com/yangshun", "newsletter": "https://www.greatfrontend.com"}, "imageURL": "https://github.com/yangshun.png", "key": "yang<PERSON>n"}, "slorber": {"name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Docusaurus maintainer", "url": "https://sebastienlorber.com", "page": {"permalink": "/blog/authors/all-sebastien-lorber-articles"}, "socials": {"x": "https://x.com/sebastienlorber", "linkedin": "https://www.linkedin.com/in/sebastienlorber/", "github": "https://github.com/slorber", "newsletter": "https://thisweekinreact.com"}, "imageURL": "https://github.com/slorber.png", "key": "slorber"}}}}, "docusaurus-plugin-content-pages": {"default": [{"type": "jsx", "permalink": "/", "source": "@site/src/pages/index.tsx"}, {"type": "mdx", "permalink": "/markdown-page", "source": "@site/src/pages/markdown-page.md", "title": "Markdown page example", "description": "You don't need React to write simple standalone pages.", "frontMatter": {"title": "Markdown page example"}, "unlisted": false}]}, "docusaurus-plugin-debug": {}, "docusaurus-plugin-svgr": {}, "docusaurus-theme-classic": {}, "docusaurus-theme-live-codeblock": {}, "docusaurus-bootstrap-plugin": {}, "docusaurus-mdx-fallback-plugin": {}}}