# 可执行代码示例

这个页面展示了如何在Docusaurus中嵌入可执行的代码示例。

## React组件示例

下面是一个简单的React组件，你可以直接在浏览器中编辑和运行：

```jsx live
function Button() {
  const [count, setCount] = React.useState(0);
  
  return (
    <div>
      <p>你点击了 {count} 次</p>
      <button onClick={() => setCount(count + 1)}>
        点击我
      </button>
    </div>
  );
}
```

## 计算器示例

这是一个更复杂的计算器组件：

```jsx live
function Calculator() {
  const [display, setDisplay] = React.useState('0');
  const [previousValue, setPreviousValue] = React.useState(null);
  const [operation, setOperation] = React.useState(null);
  const [waitingForOperand, setWaitingForOperand] = React.useState(false);

  const inputNumber = (num) => {
    if (waitingForOperand) {
      setDisplay(String(num));
      setWaitingForOperand(false);
    } else {
      setDisplay(display === '0' ? String(num) : display + num);
    }
  };

  const inputOperation = (nextOperation) => {
    const inputValue = parseFloat(display);

    if (previousValue === null) {
      setPreviousValue(inputValue);
    } else if (operation) {
      const currentValue = previousValue || 0;
      const newValue = calculate(currentValue, inputValue, operation);

      setDisplay(String(newValue));
      setPreviousValue(newValue);
    }

    setWaitingForOperand(true);
    setOperation(nextOperation);
  };

  const calculate = (firstValue, secondValue, operation) => {
    switch (operation) {
      case '+':
        return firstValue + secondValue;
      case '-':
        return firstValue - secondValue;
      case '*':
        return firstValue * secondValue;
      case '/':
        return firstValue / secondValue;
      case '=':
        return secondValue;
      default:
        return secondValue;
    }
  };

  const performCalculation = () => {
    const inputValue = parseFloat(display);

    if (previousValue !== null && operation) {
      const newValue = calculate(previousValue, inputValue, operation);
      setDisplay(String(newValue));
      setPreviousValue(null);
      setOperation(null);
      setWaitingForOperand(true);
    }
  };

  const clear = () => {
    setDisplay('0');
    setPreviousValue(null);
    setOperation(null);
    setWaitingForOperand(false);
  };

  return (
    <div style={{ 
      maxWidth: '300px', 
      margin: '0 auto',
      border: '1px solid #ccc',
      borderRadius: '8px',
      padding: '20px',
      backgroundColor: '#f9f9f9'
    }}>
      <div style={{
        backgroundColor: '#000',
        color: '#fff',
        padding: '10px',
        textAlign: 'right',
        fontSize: '24px',
        marginBottom: '10px',
        borderRadius: '4px'
      }}>
        {display}
      </div>
      
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: '5px' }}>
        <button onClick={clear} style={{ gridColumn: 'span 2', padding: '15px', fontSize: '16px' }}>
          清除
        </button>
        <button onClick={() => inputOperation('/')} style={{ padding: '15px', fontSize: '16px' }}>
          ÷
        </button>
        <button onClick={() => inputOperation('*')} style={{ padding: '15px', fontSize: '16px' }}>
          ×
        </button>
        
        <button onClick={() => inputNumber(7)} style={{ padding: '15px', fontSize: '16px' }}>7</button>
        <button onClick={() => inputNumber(8)} style={{ padding: '15px', fontSize: '16px' }}>8</button>
        <button onClick={() => inputNumber(9)} style={{ padding: '15px', fontSize: '16px' }}>9</button>
        <button onClick={() => inputOperation('-')} style={{ padding: '15px', fontSize: '16px' }}>-</button>
        
        <button onClick={() => inputNumber(4)} style={{ padding: '15px', fontSize: '16px' }}>4</button>
        <button onClick={() => inputNumber(5)} style={{ padding: '15px', fontSize: '16px' }}>5</button>
        <button onClick={() => inputNumber(6)} style={{ padding: '15px', fontSize: '16px' }}>6</button>
        <button onClick={() => inputOperation('+')} style={{ padding: '15px', fontSize: '16px' }}>+</button>
        
        <button onClick={() => inputNumber(1)} style={{ padding: '15px', fontSize: '16px' }}>1</button>
        <button onClick={() => inputNumber(2)} style={{ padding: '15px', fontSize: '16px' }}>2</button>
        <button onClick={() => inputNumber(3)} style={{ padding: '15px', fontSize: '16px' }}>3</button>
        <button onClick={performCalculation} style={{ gridRow: 'span 2', padding: '15px', fontSize: '16px' }}>
          =
        </button>
        
        <button onClick={() => inputNumber(0)} style={{ gridColumn: 'span 2', padding: '15px', fontSize: '16px' }}>
          0
        </button>
        <button onClick={() => inputNumber('.')} style={{ padding: '15px', fontSize: '16px' }}>.</button>
      </div>
    </div>
  );
}
```

## 待办事项列表示例

这是一个功能完整的待办事项列表：

```jsx live
function TodoList() {
  const [todos, setTodos] = React.useState([
    { id: 1, text: '学习React', completed: false },
    { id: 2, text: '使用Docusaurus', completed: true }
  ]);
  const [inputValue, setInputValue] = React.useState('');

  const addTodo = () => {
    if (inputValue.trim() !== '') {
      setTodos([...todos, {
        id: Date.now(),
        text: inputValue,
        completed: false
      }]);
      setInputValue('');
    }
  };

  const toggleTodo = (id) => {
    setTodos(todos.map(todo =>
      todo.id === id ? { ...todo, completed: !todo.completed } : todo
    ));
  };

  const deleteTodo = (id) => {
    setTodos(todos.filter(todo => todo.id !== id));
  };

  return (
    <div style={{ maxWidth: '400px', margin: '0 auto', padding: '20px' }}>
      <h3>待办事项列表</h3>
      
      <div style={{ marginBottom: '20px', display: 'flex', gap: '10px' }}>
        <input
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && addTodo()}
          placeholder="添加新的待办事项..."
          style={{ 
            flex: 1, 
            padding: '8px', 
            border: '1px solid #ccc', 
            borderRadius: '4px' 
          }}
        />
        <button 
          onClick={addTodo}
          style={{ 
            padding: '8px 16px', 
            backgroundColor: '#007bff', 
            color: 'white', 
            border: 'none', 
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          添加
        </button>
      </div>

      <ul style={{ listStyle: 'none', padding: 0 }}>
        {todos.map(todo => (
          <li 
            key={todo.id} 
            style={{ 
              display: 'flex', 
              alignItems: 'center', 
              padding: '10px', 
              border: '1px solid #eee', 
              borderRadius: '4px', 
              marginBottom: '5px',
              backgroundColor: todo.completed ? '#f8f9fa' : 'white'
            }}
          >
            <input
              type="checkbox"
              checked={todo.completed}
              onChange={() => toggleTodo(todo.id)}
              style={{ marginRight: '10px' }}
            />
            <span 
              style={{ 
                flex: 1, 
                textDecoration: todo.completed ? 'line-through' : 'none',
                color: todo.completed ? '#6c757d' : 'inherit'
              }}
            >
              {todo.text}
            </span>
            <button 
              onClick={() => deleteTodo(todo.id)}
              style={{ 
                backgroundColor: '#dc3545', 
                color: 'white', 
                border: 'none', 
                borderRadius: '4px', 
                padding: '4px 8px',
                cursor: 'pointer'
              }}
            >
              删除
            </button>
          </li>
        ))}
      </ul>
      
      <div style={{ marginTop: '20px', fontSize: '14px', color: '#6c757d' }}>
        总计: {todos.length} 项，已完成: {todos.filter(t => t.completed).length} 项
      </div>
    </div>
  );
}
```

## 使用说明

1. **可执行代码块**：使用 `jsx live` 标记的代码块可以直接在页面中运行
2. **实时编辑**：你可以直接在代码编辑器中修改代码，结果会实时更新
3. **React支持**：支持完整的React功能，包括Hooks、状态管理等
4. **样式支持**：可以使用内联样式或CSS类名

## 注意事项

- 代码在浏览器中运行，请注意性能和安全性
- 支持ES6+语法和React Hooks
- 可以导入一些常用的库（需要在配置中指定）
- 适合用于文档、教程和演示
