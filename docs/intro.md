---
sidebar_position: 1
---

# Docusaurus 可执行代码示例

欢迎来到 **Docusaurus 可执行代码示例** 项目！

这个项目展示了如何在 Docusaurus 文档中嵌入可执行的 React 代码示例。

## 🚀 主要特性

- **实时代码编辑**：直接在浏览器中编辑和运行 React 代码
- **即时预览**：代码修改后立即看到结果
- **完整的 React 支持**：支持 Hooks、状态管理、事件处理等
- **中文界面**：完全中文化的用户界面

## 📚 示例内容

本项目包含以下示例：

### 基础示例
- 简单的计数器组件
- 功能完整的计算器
- 待办事项列表

### 高级示例
- 数据可视化图表
- 表单验证
- 实时数据仪表板

## 🛠️ 技术栈

- **Docusaurus 3.x**：现代化的文档站点生成器
- **React 19**：最新版本的 React
- **TypeScript**：类型安全的 JavaScript
- **Live Codeblock**：支持可执行代码块的主题

## 📖 如何使用

### 创建可执行代码块

使用 `jsx live` 标记来创建可执行的代码块：

````markdown
```jsx live
function MyComponent() {
  const [count, setCount] = React.useState(0);

  return (
    <div>
      <p>计数: {count}</p>
      <button onClick={() => setCount(count + 1)}>
        点击增加
      </button>
    </div>
  );
}
```
````

### 支持的功能

- ✅ React Hooks (useState, useEffect, etc.)
- ✅ 事件处理
- ✅ 条件渲染
- ✅ 列表渲染
- ✅ 内联样式
- ✅ 表单处理
- ✅ 本地状态管理

## 🎯 适用场景

这个项目特别适合：

- **技术文档**：展示 API 使用方法
- **教程网站**：提供交互式学习体验
- **组件库文档**：实时演示组件效果
- **代码示例**：让读者直接体验代码运行结果

## 🚀 快速开始

1. 克隆项目
2. 安装依赖：`pnpm install`
3. 启动开发服务器：`pnpm start`
4. 在浏览器中打开 http://localhost:3000

开始探索可执行代码示例吧！
