# 高级可执行示例

这里展示一些更复杂的React组件示例。

## 图表组件示例

一个简单的柱状图组件：

```jsx live
function BarChart() {
  const [data, setData] = React.useState([
    { name: 'React', value: 85 },
    { name: 'Vue', value: 70 },
    { name: 'Angular', value: 60 },
    { name: '<PERSON>vel<PERSON>', value: 45 }
  ]);

  const maxValue = Math.max(...data.map(item => item.value));

  const addRandomData = () => {
    const frameworks = ['Next.js', 'Nuxt.js', 'Gatsby', 'Remix', 'SvelteKit'];
    const randomFramework = frameworks[Math.floor(Math.random() * frameworks.length)];
    const randomValue = Math.floor(Math.random() * 100) + 1;
    
    setData([...data, { name: randomFramework, value: randomValue }]);
  };

  const clearData = () => {
    setData([
      { name: 'React', value: 85 },
      { name: 'Vue', value: 70 },
      { name: 'Angular', value: 60 },
      { name: '<PERSON>vel<PERSON>', value: 45 }
    ]);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '600px', margin: '0 auto' }}>
      <h3>前端框架流行度</h3>
      
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={addRandomData}
          style={{ 
            marginRight: '10px',
            padding: '8px 16px', 
            backgroundColor: '#28a745', 
            color: 'white', 
            border: 'none', 
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          添加随机数据
        </button>
        <button 
          onClick={clearData}
          style={{ 
            padding: '8px 16px', 
            backgroundColor: '#dc3545', 
            color: 'white', 
            border: 'none', 
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          重置数据
        </button>
      </div>

      <div style={{ 
        display: 'flex', 
        alignItems: 'end', 
        height: '300px', 
        border: '1px solid #ccc',
        padding: '20px',
        backgroundColor: '#f8f9fa'
      }}>
        {data.map((item, index) => (
          <div 
            key={index}
            style={{ 
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              marginRight: '10px',
              flex: 1
            }}
          >
            <div style={{ 
              fontSize: '12px', 
              marginBottom: '5px',
              fontWeight: 'bold'
            }}>
              {item.value}%
            </div>
            <div 
              style={{ 
                width: '100%',
                height: `${(item.value / maxValue) * 200}px`,
                backgroundColor: `hsl(${index * 60}, 70%, 50%)`,
                borderRadius: '4px 4px 0 0',
                transition: 'height 0.3s ease'
              }}
            />
            <div style={{ 
              marginTop: '10px', 
              fontSize: '12px',
              textAlign: 'center',
              transform: 'rotate(-45deg)',
              transformOrigin: 'center'
            }}>
              {item.name}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
```

## 表单验证示例

一个带有实时验证的表单：

```jsx live
function ValidatedForm() {
  const [formData, setFormData] = React.useState({
    email: '',
    password: '',
    confirmPassword: '',
    name: ''
  });
  
  const [errors, setErrors] = React.useState({});
  const [submitted, setSubmitted] = React.useState(false);

  const validateEmail = (email) => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = '姓名不能为空';
    }

    if (!formData.email) {
      newErrors.email = '邮箱不能为空';
    } else if (!validateEmail(formData.email)) {
      newErrors.email = '请输入有效的邮箱地址';
    }

    if (!formData.password) {
      newErrors.password = '密码不能为空';
    } else if (formData.password.length < 6) {
      newErrors.password = '密码至少需要6个字符';
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = '两次输入的密码不一致';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (field, value) => {
    setFormData({ ...formData, [field]: value });
    
    // 实时验证
    if (submitted) {
      setTimeout(() => validateForm(), 100);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setSubmitted(true);
    
    if (validateForm()) {
      alert('表单提交成功！\n' + JSON.stringify(formData, null, 2));
    }
  };

  const inputStyle = (hasError) => ({
    width: '100%',
    padding: '10px',
    border: `1px solid ${hasError ? '#dc3545' : '#ccc'}`,
    borderRadius: '4px',
    fontSize: '14px'
  });

  const errorStyle = {
    color: '#dc3545',
    fontSize: '12px',
    marginTop: '5px'
  };

  return (
    <div style={{ maxWidth: '400px', margin: '0 auto', padding: '20px' }}>
      <h3>用户注册表单</h3>
      
      <form onSubmit={handleSubmit}>
        <div style={{ marginBottom: '15px' }}>
          <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
            姓名
          </label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => handleChange('name', e.target.value)}
            style={inputStyle(errors.name)}
            placeholder="请输入您的姓名"
          />
          {errors.name && <div style={errorStyle}>{errors.name}</div>}
        </div>

        <div style={{ marginBottom: '15px' }}>
          <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
            邮箱
          </label>
          <input
            type="email"
            value={formData.email}
            onChange={(e) => handleChange('email', e.target.value)}
            style={inputStyle(errors.email)}
            placeholder="请输入您的邮箱"
          />
          {errors.email && <div style={errorStyle}>{errors.email}</div>}
        </div>

        <div style={{ marginBottom: '15px' }}>
          <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
            密码
          </label>
          <input
            type="password"
            value={formData.password}
            onChange={(e) => handleChange('password', e.target.value)}
            style={inputStyle(errors.password)}
            placeholder="请输入密码（至少6位）"
          />
          {errors.password && <div style={errorStyle}>{errors.password}</div>}
        </div>

        <div style={{ marginBottom: '20px' }}>
          <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
            确认密码
          </label>
          <input
            type="password"
            value={formData.confirmPassword}
            onChange={(e) => handleChange('confirmPassword', e.target.value)}
            style={inputStyle(errors.confirmPassword)}
            placeholder="请再次输入密码"
          />
          {errors.confirmPassword && <div style={errorStyle}>{errors.confirmPassword}</div>}
        </div>

        <button
          type="submit"
          style={{
            width: '100%',
            padding: '12px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            fontSize: '16px',
            cursor: 'pointer'
          }}
        >
          注册
        </button>
      </form>

      {submitted && Object.keys(errors).length === 0 && (
        <div style={{
          marginTop: '20px',
          padding: '10px',
          backgroundColor: '#d4edda',
          border: '1px solid #c3e6cb',
          borderRadius: '4px',
          color: '#155724'
        }}>
          ✅ 表单验证通过！
        </div>
      )}
    </div>
  );
}
```

## 数据可视化示例

一个简单的数据仪表板：

```jsx live
function Dashboard() {
  const [metrics, setMetrics] = React.useState({
    users: 1234,
    revenue: 56789,
    orders: 890,
    conversion: 3.45
  });

  const [isAnimating, setIsAnimating] = React.useState(false);

  const animateMetrics = () => {
    setIsAnimating(true);
    
    const interval = setInterval(() => {
      setMetrics(prev => ({
        users: prev.users + Math.floor(Math.random() * 10) - 5,
        revenue: prev.revenue + Math.floor(Math.random() * 1000) - 500,
        orders: prev.orders + Math.floor(Math.random() * 5) - 2,
        conversion: Math.max(0, prev.conversion + (Math.random() * 0.2) - 0.1)
      }));
    }, 100);

    setTimeout(() => {
      clearInterval(interval);
      setIsAnimating(false);
    }, 2000);
  };

  const MetricCard = ({ title, value, unit, color, icon }) => (
    <div style={{
      backgroundColor: 'white',
      padding: '20px',
      borderRadius: '8px',
      boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
      border: `3px solid ${color}`,
      textAlign: 'center',
      transition: 'transform 0.2s ease',
      cursor: 'pointer'
    }}
    onMouseEnter={(e) => e.target.style.transform = 'scale(1.05)'}
    onMouseLeave={(e) => e.target.style.transform = 'scale(1)'}
    >
      <div style={{ fontSize: '24px', marginBottom: '10px' }}>{icon}</div>
      <div style={{ fontSize: '24px', fontWeight: 'bold', color: color, marginBottom: '5px' }}>
        {typeof value === 'number' ? value.toLocaleString() : value}{unit}
      </div>
      <div style={{ fontSize: '14px', color: '#666' }}>{title}</div>
    </div>
  );

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '30px'
      }}>
        <h3 style={{ margin: 0 }}>实时数据仪表板</h3>
        <button
          onClick={animateMetrics}
          disabled={isAnimating}
          style={{
            padding: '10px 20px',
            backgroundColor: isAnimating ? '#6c757d' : '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: isAnimating ? 'not-allowed' : 'pointer'
          }}
        >
          {isAnimating ? '更新中...' : '刷新数据'}
        </button>
      </div>

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(180px, 1fr))',
        gap: '20px',
        marginBottom: '30px'
      }}>
        <MetricCard
          title="总用户数"
          value={metrics.users}
          unit=""
          color="#007bff"
          icon="👥"
        />
        <MetricCard
          title="总收入"
          value={metrics.revenue}
          unit="¥"
          color="#28a745"
          icon="💰"
        />
        <MetricCard
          title="订单数量"
          value={metrics.orders}
          unit=""
          color="#ffc107"
          icon="📦"
        />
        <MetricCard
          title="转化率"
          value={metrics.conversion.toFixed(2)}
          unit="%"
          color="#dc3545"
          icon="📈"
        />
      </div>

      <div style={{
        backgroundColor: 'white',
        padding: '20px',
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
      }}>
        <h4>数据趋势</h4>
        <div style={{ 
          height: '100px', 
          backgroundColor: '#f8f9fa',
          borderRadius: '4px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#6c757d'
        }}>
          📊 这里可以放置图表组件
        </div>
      </div>
    </div>
  );
}
```

## 特性说明

这些示例展示了：

1. **状态管理**：使用React Hooks管理复杂状态
2. **事件处理**：响应用户交互
3. **条件渲染**：根据状态动态显示内容
4. **样式应用**：内联样式和动态样式
5. **表单处理**：输入验证和提交
6. **动画效果**：CSS过渡和JavaScript动画
7. **数据可视化**：简单的图表和仪表板

所有代码都可以直接在浏览器中编辑和运行！
